#!/usr/bin/env python3
"""
Web interface for Plant Recognition System
"""

from flask import Flask, request, render_template, jsonify, redirect, url_for
import os
import json
from werkzeug.utils import secure_filename
from simple_plant_classifier import SimplePlantClassifier
import tempfile

app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Initialize classifier
classifier = SimplePlantClassifier()

# Try to load existing model
model_loaded = classifier.load_model("plant_model.pkl")

ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/')
def index():
    return render_template('index.html', model_loaded=model_loaded)

@app.route('/upload', methods=['POST'])
def upload_file():
    if not model_loaded:
        return jsonify({'error': 'Model not loaded. Please train the model first.'}), 400
    
    if 'file' not in request.files:
        return jsonify({'error': 'No file uploaded'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No file selected'}), 400
    
    if file and allowed_file(file.filename):
        # Save file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix='.jpg') as tmp_file:
            file.save(tmp_file.name)
            
            # Classify the image
            result = classifier.predict(tmp_file.name)
            
            # Clean up temporary file
            os.unlink(tmp_file.name)
            
            if result:
                return jsonify(result)
            else:
                return jsonify({'error': 'Failed to classify image'}), 500
    
    return jsonify({'error': 'Invalid file type'}), 400

@app.route('/train', methods=['POST'])
def train_model():
    global model_loaded
    
    try:
        # Train the model
        training_dir = "training_plant_images"
        if classifier.train(training_dir):
            classifier.save_model("plant_model.pkl")
            model_loaded = True
            return jsonify({'success': True, 'message': 'Model trained successfully!'})
        else:
            return jsonify({'success': False, 'message': 'Training failed'}), 500
    except Exception as e:
        return jsonify({'success': False, 'message': f'Training error: {str(e)}'}), 500

@app.route('/test/<image_name>')
def test_image(image_name):
    if not model_loaded:
        return jsonify({'error': 'Model not loaded'}), 400
    
    image_path = f"testing_dataset/{image_name}"
    if os.path.exists(image_path):
        result = classifier.predict(image_path)
        if result:
            return jsonify(result)
        else:
            return jsonify({'error': 'Failed to classify image'}), 500
    else:
        return jsonify({'error': 'Image not found'}), 404

if __name__ == '__main__':
    # Create templates directory if it doesn't exist
    os.makedirs('templates', exist_ok=True)
    
    # Create a simple HTML template if it doesn't exist
    template_path = 'templates/index.html'
    if not os.path.exists(template_path):
        html_content = '''<!DOCTYPE html>
<html>
<head>
    <title>Plant Recognition System</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .container { background: #f5f5f5; padding: 20px; border-radius: 10px; margin: 20px 0; }
        .result { background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .error { background: #ffe8e8; padding: 15px; border-radius: 5px; margin: 10px 0; }
        button { background: #4CAF50; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #45a049; }
        button:disabled { background: #cccccc; cursor: not-allowed; }
        input[type="file"] { margin: 10px 0; }
        .predictions { margin: 10px 0; }
        .prediction-item { margin: 5px 0; padding: 5px; background: white; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>🌿 Plant Recognition System</h1>
    
    {% if model_loaded %}
        <div class="container">
            <h2>Upload Image for Classification</h2>
            <input type="file" id="fileInput" accept="image/*">
            <button onclick="uploadFile()">Classify Plant</button>
            <div id="result"></div>
        </div>
        
        <div class="container">
            <h2>Test with Sample Images</h2>
            <button onclick="testImage('1.jpg')">Test Image 1</button>
            <button onclick="testImage('5.jpg')">Test Image 5</button>
            <button onclick="testImage('10.jpg')">Test Image 10</button>
        </div>
    {% else %}
        <div class="container">
            <h2>Model Not Loaded</h2>
            <p>The plant recognition model needs to be trained first.</p>
            <button onclick="trainModel()" id="trainBtn">Train Model</button>
            <div id="trainResult"></div>
        </div>
    {% endif %}

    <script>
        function uploadFile() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) {
                alert('Please select a file first');
                return;
            }
            
            const formData = new FormData();
            formData.append('file', file);
            
            document.getElementById('result').innerHTML = '<p>Classifying...</p>';
            
            fetch('/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    document.getElementById('result').innerHTML = `<div class="error">Error: ${data.error}</div>`;
                } else {
                    displayResult(data);
                }
            })
            .catch(error => {
                document.getElementById('result').innerHTML = `<div class="error">Error: ${error}</div>`;
            });
        }
        
        function testImage(imageName) {
            fetch(`/test/${imageName}`)
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    document.getElementById('result').innerHTML = `<div class="error">Error: ${data.error}</div>`;
                } else {
                    displayResult(data);
                }
            })
            .catch(error => {
                document.getElementById('result').innerHTML = `<div class="error">Error: ${error}</div>`;
            });
        }
        
        function displayResult(data) {
            let html = `<div class="result">
                <h3>Classification Result</h3>
                <p><strong>Predicted Plant:</strong> ${data.result}</p>
                <p><strong>Confidence:</strong> ${data.result_confidence}</p>
                <div class="predictions">
                    <h4>All Predictions:</h4>`;
            
            for (const [plant, confidence] of Object.entries(data.predictions)) {
                const percentage = (parseFloat(confidence) * 100).toFixed(1);
                html += `<div class="prediction-item">${plant}: ${percentage}%</div>`;
            }
            
            html += '</div></div>';
            document.getElementById('result').innerHTML = html;
        }
        
        function trainModel() {
            const trainBtn = document.getElementById('trainBtn');
            trainBtn.disabled = true;
            trainBtn.textContent = 'Training...';
            
            document.getElementById('trainResult').innerHTML = '<p>Training model, please wait...</p>';
            
            fetch('/train', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('trainResult').innerHTML = `<div class="result">${data.message}</div>`;
                    setTimeout(() => location.reload(), 2000);
                } else {
                    document.getElementById('trainResult').innerHTML = `<div class="error">${data.message}</div>`;
                }
                trainBtn.disabled = false;
                trainBtn.textContent = 'Train Model';
            })
            .catch(error => {
                document.getElementById('trainResult').innerHTML = `<div class="error">Error: ${error}</div>`;
                trainBtn.disabled = false;
                trainBtn.textContent = 'Train Model';
            });
        }
    </script>
</body>
</html>'''
        
        with open(template_path, 'w') as f:
            f.write(html_content)
    
    print("Starting Plant Recognition Web App...")
    print("Open your browser and go to: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)
