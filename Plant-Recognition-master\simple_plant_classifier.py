#!/usr/bin/env python3
"""
Simple Plant Recognition System
A simplified version that works with modern Python libraries
"""

import os
import sys
import json
import numpy as np
from PIL import Image
import matplotlib.pyplot as plt
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report
import pickle
from pathlib import Path

class SimplePlantClassifier:
    def __init__(self):
        self.model = RandomForestClassifier(n_estimators=100, random_state=42)
        self.label_encoder = {}
        self.reverse_label_encoder = {}
        self.is_trained = False
        
    def extract_features(self, image_path, target_size=(64, 64)):
        """Extract simple features from an image"""
        try:
            # Load and resize image
            img = Image.open(image_path)
            img = img.convert('RGB')
            img = img.resize(target_size)
            
            # Convert to numpy array and flatten
            img_array = np.array(img)
            
            # Extract basic features
            features = []
            
            # Color histogram features
            for channel in range(3):  # RGB channels
                hist, _ = np.histogram(img_array[:,:,channel], bins=16, range=(0, 256))
                features.extend(hist)
            
            # Basic statistical features
            features.extend([
                np.mean(img_array),
                np.std(img_array),
                np.min(img_array),
                np.max(img_array)
            ])
            
            # Flatten the image as additional features (reduced resolution)
            small_img = img.resize((16, 16))
            features.extend(np.array(small_img).flatten())
            
            return np.array(features)
            
        except Exception as e:
            print(f"Error processing {image_path}: {e}")
            return None
    
    def load_training_data(self, training_dir):
        """Load training data from directory structure"""
        features = []
        labels = []
        
        training_path = Path(training_dir)
        if not training_path.exists():
            print(f"Training directory {training_dir} not found!")
            return None, None
        
        # Get all plant categories
        categories = [d for d in training_path.iterdir() if d.is_dir()]
        
        # Create label encoding
        for i, category in enumerate(categories):
            category_name = category.name
            self.label_encoder[category_name] = i
            self.reverse_label_encoder[i] = category_name
        
        print(f"Found {len(categories)} plant categories:")
        for cat in categories:
            print(f"  - {cat.name}")
        
        # Load images from each category
        for category in categories:
            category_name = category.name
            label = self.label_encoder[category_name]
            
            image_files = list(category.glob("*.jpg")) + list(category.glob("*.jpeg")) + list(category.glob("*.png"))
            print(f"Loading {len(image_files)} images from {category_name}...")
            
            for img_file in image_files:
                feature_vector = self.extract_features(str(img_file))
                if feature_vector is not None:
                    features.append(feature_vector)
                    labels.append(label)
        
        return np.array(features), np.array(labels)
    
    def train(self, training_dir):
        """Train the classifier"""
        print("Loading training data...")
        X, y = self.load_training_data(training_dir)
        
        if X is None or len(X) == 0:
            print("No training data found!")
            return False
        
        print(f"Training with {len(X)} samples...")
        
        # Split data for validation
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        # Train the model
        self.model.fit(X_train, y_train)
        
        # Validate
        y_pred = self.model.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        
        print(f"Training completed!")
        print(f"Validation accuracy: {accuracy:.2f}")
        print("\nClassification Report:")
        print(classification_report(y_test, y_pred, target_names=list(self.label_encoder.keys())))
        
        self.is_trained = True
        return True
    
    def predict(self, image_path):
        """Predict the plant type for a given image"""
        if not self.is_trained:
            print("Model not trained yet!")
            return None
        
        features = self.extract_features(image_path)
        if features is None:
            return None
        
        # Get prediction probabilities
        probabilities = self.model.predict_proba([features])[0]
        predicted_class = self.model.predict([features])[0]
        
        # Create result dictionary
        result = {
            "predictions": {},
            "result": self.reverse_label_encoder[predicted_class],
            "result_confidence": f"{probabilities[predicted_class] * 100:.1f}%"
        }
        
        # Add all predictions
        for i, prob in enumerate(probabilities):
            plant_name = self.reverse_label_encoder[i]
            result["predictions"][plant_name] = f"{prob:.6f}"
        
        return result
    
    def save_model(self, model_path):
        """Save the trained model"""
        if not self.is_trained:
            print("No trained model to save!")
            return False
        
        model_data = {
            'model': self.model,
            'label_encoder': self.label_encoder,
            'reverse_label_encoder': self.reverse_label_encoder
        }
        
        with open(model_path, 'wb') as f:
            pickle.dump(model_data, f)
        
        print(f"Model saved to {model_path}")
        return True
    
    def load_model(self, model_path):
        """Load a trained model"""
        try:
            with open(model_path, 'rb') as f:
                model_data = pickle.load(f)
            
            self.model = model_data['model']
            self.label_encoder = model_data['label_encoder']
            self.reverse_label_encoder = model_data['reverse_label_encoder']
            self.is_trained = True
            
            print(f"Model loaded from {model_path}")
            return True
        except Exception as e:
            print(f"Error loading model: {e}")
            return False

def main():
    if len(sys.argv) < 2:
        print("Usage:")
        print("  Training: python simple_plant_classifier.py train")
        print("  Classification: python simple_plant_classifier.py classify <image_path>")
        return
    
    classifier = SimplePlantClassifier()
    
    if sys.argv[1] == "train":
        # Train the model
        training_dir = "training_plant_images"
        if classifier.train(training_dir):
            classifier.save_model("plant_model.pkl")
        
    elif sys.argv[1] == "classify":
        if len(sys.argv) < 3:
            print("Please provide an image path for classification")
            return
        
        image_path = sys.argv[2]
        
        # Try to load existing model
        if not classifier.load_model("plant_model.pkl"):
            print("No trained model found. Please train first using: python simple_plant_classifier.py train")
            return
        
        # Classify the image
        result = classifier.predict(image_path)
        if result:
            print(json.dumps(result, indent=2))
        else:
            print("Failed to classify image")
    
    else:
        print("Unknown command. Use 'train' or 'classify'")

if __name__ == "__main__":
    main()
